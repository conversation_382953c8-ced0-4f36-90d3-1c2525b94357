// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initScrollEffects();
    initServiceCategories();
    initSmoothScrolling();
    initMobileOptimizations();
});

// Navigation functionality
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    navToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = navToggle.contains(event.target) || navMenu.contains(event.target);
        
        if (!isClickInsideNav && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
    });
}

// Scroll effects for navbar
function initScrollEffects() {
    const navbar = document.getElementById('navbar');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add/remove scrolled class for styling
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar on scroll (optional enhancement)
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            // Scrolling down
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Highlight active navigation link based on scroll position
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');

    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (window.pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
}

// Service category buttons functionality
function initServiceCategories() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);

            // Handle category-specific actions
            handleCategoryClick(category);
        });
    });
}

// Handle category button clicks
function handleCategoryClick(category) {
    // You can customize these actions based on your needs
    switch(category) {
        case 'plumbing':
            scrollToServices();
            highlightServiceCards(['Emergency Plumbing', 'Leaking Taps & Toilets']);
            break;
        case 'drains':
            scrollToServices();
            highlightServiceCards(['Blocked Drains']);
            break;
        case 'gas':
            scrollToServices();
            highlightServiceCards(['Gas Pipe Installation']);
            break;
        case 'hotwater':
            scrollToServices();
            highlightServiceCards(['Hot Water Repairs']);
            break;
        default:
            scrollToServices();
    }
}

// Scroll to services section
function scrollToServices() {
    const servicesSection = document.getElementById('services');
    servicesSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
}

// Highlight specific service cards
function highlightServiceCards(serviceNames) {
    const serviceCards = document.querySelectorAll('.service-card');
    
    // Remove previous highlights
    serviceCards.forEach(card => {
        card.classList.remove('highlighted');
    });
    
    // Add highlight to matching cards
    serviceCards.forEach(card => {
        const cardTitle = card.querySelector('h3').textContent;
        if (serviceNames.some(name => cardTitle.includes(name))) {
            card.classList.add('highlighted');
            
            // Remove highlight after 3 seconds
            setTimeout(() => {
                card.classList.remove('highlighted');
            }, 3000);
        }
    });
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Mobile-specific optimizations
function initMobileOptimizations() {
    // Show/hide mobile call button based on scroll
    const mobileCallBtn = document.querySelector('.mobile-call-btn');
    
    if (mobileCallBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                mobileCallBtn.style.opacity = '1';
                mobileCallBtn.style.visibility = 'visible';
            } else {
                mobileCallBtn.style.opacity = '0';
                mobileCallBtn.style.visibility = 'hidden';
            }
        });
    }

    // Add touch feedback for mobile buttons
    const buttons = document.querySelectorAll('button, .btn, .nav-cta, .emergency-btn, .category-btn');
    
    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        button.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });
}

// Intersection Observer for animations (optional enhancement)
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .contact-item, .about-text, .about-image');
    animateElements.forEach(el => observer.observe(el));
}

// Call to action tracking (for analytics)
function trackCTAClicks() {
    const ctaButtons = document.querySelectorAll('.nav-cta, .emergency-btn, .mobile-call-btn a');
    
    ctaButtons.forEach(button => {
        button.addEventListener('click', function() {
            // You can add analytics tracking here
            console.log('CTA clicked:', this.textContent || 'Mobile call button');
            
            // Example: Google Analytics event tracking
            // gtag('event', 'click', {
            //     event_category: 'CTA',
            //     event_label: this.textContent || 'Mobile call button'
            // });
        });
    });
}

// Form handling (if you add contact forms later)
function initFormHandling() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Add form validation and submission logic here
            console.log('Form submitted');
            
            // Show success message
            showNotification('Thank you! We\'ll get back to you soon.', 'success');
        });
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : '#0066CC'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initScrollAnimations();
    trackCTAClicks();
    initFormHandling();
});

// Performance optimization: Debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll events for better performance
const debouncedScrollHandler = debounce(function() {
    // Any heavy scroll-based calculations can go here
}, 10);

window.addEventListener('scroll', debouncedScrollHandler);
