/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo img {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #0066CC;
}

.nav-cta {
    background: #DC2626;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-cta:hover {
    background: #B91C1C;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 102, 204, 0.8), rgba(0, 102, 204, 0.8)),
                url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.emergency-btn {
    display: inline-block;
    background: #DC2626;
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(220, 38, 38, 0.3);
}

.emergency-btn:hover {
    background: #B91C1C;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    color: #0066CC;
    margin-bottom: 1rem;
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: #f8fafc;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 102, 204, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0066CC, #0052A3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.service-card h3 {
    color: #0066CC;
    margin-bottom: 1rem;
}

/* Service Categories */
.service-categories {
    padding: 3rem 0;
    background: white;
}

.service-categories h2 {
    text-align: center;
    color: #0066CC;
    margin-bottom: 2rem;
}

.category-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.category-btn {
    background: white;
    border: 2px solid #0066CC;
    color: #0066CC;
    padding: 1.5rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.category-btn:hover {
    background: #0066CC;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 102, 204, 0.3);
}

/* About Section */
.about {
    padding: 5rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    color: #0066CC;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #0066CC;
    font-weight: 500;
}

.feature i {
    font-size: 1.5rem;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Trust Section */
.trust-section {
    padding: 2rem 0;
    background: #f8fafc;
}

.trust-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #0066CC;
    font-weight: 600;
    font-size: 1.1rem;
}

.payment-methods {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #666;
}

.payment-icons {
    display: flex;
    gap: 0.5rem;
    font-size: 1.5rem;
    color: #0066CC;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: white;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 15px;
    border-left: 4px solid #0066CC;
}

.contact-item i {
    font-size: 2rem;
    color: #0066CC;
}

.contact-item h3 {
    color: #0066CC;
    margin-bottom: 0.5rem;
}

.contact-item a {
    color: #DC2626;
    text-decoration: none;
    font-weight: 600;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background: #1a202c;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
    margin-bottom: 1rem;
    filter: brightness(0) invert(1); /* Makes logo white for dark footer */
}

.footer-section h3 {
    color: #0066CC;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #cbd5e0;
    margin-bottom: 0.5rem;
}

.footer-payment {
    display: flex;
    gap: 1rem;
    font-size: 1.5rem;
    color: #0066CC;
}

.footer-bottom {
    border-top: 1px solid #2d3748;
    padding-top: 2rem;
    text-align: center;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: #cbd5e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #0066CC;
}

/* Mobile Call Button */
.mobile-call-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: none;
}

.mobile-call-btn a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #DC2626;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.5rem;
    box-shadow: 0 5px 20px rgba(220, 38, 38, 0.3);
    transition: all 0.3s ease;
}

.mobile-call-btn a:hover {
    background: #B91C1C;
    transform: scale(1.1);
}

/* Animations and Highlights */
.service-card.highlighted {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(220, 38, 38, 0.2);
    border: 2px solid #DC2626;
}

.animate-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 30px rgba(0, 0, 0, 0.15);
}

.nav-link.active {
    color: #0066CC;
    font-weight: 600;
}

/* Loading animations */
.service-card,
.contact-item,
.about-text,
.about-image {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .trust-content {
        justify-content: center;
        text-align: center;
    }

    .mobile-call-btn {
        display: block;
    }

    .nav-cta {
        margin-top: 1rem;
    }

    .nav-logo img {
        height: 100px;
        max-width: 1000px;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .category-buttons {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Sponsorship Page Styles */
.sponsorship-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #0066CC, #0052A3);
    color: white;
    text-align: center;
}

.sponsorship-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.sponsorship-content p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
}

.sponsorship-info {
    padding: 5rem 0;
    background: #f8fafc;
}

.sponsorship-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.sponsorship-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.sponsorship-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 102, 204, 0.15);
}

.sponsorship-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0066CC, #0052A3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.sponsorship-card h3 {
    color: #0066CC;
    margin-bottom: 1rem;
}

.sponsorship-application {
    padding: 5rem 0;
    background: white;
}

.application-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.application-text h2 {
    color: #0066CC;
    margin-bottom: 1rem;
}

.application-criteria {
    margin: 2rem 0;
}

.application-criteria h3 {
    color: #0066CC;
    margin-bottom: 1rem;
}

.application-criteria ul {
    list-style: none;
    padding: 0;
}

.application-criteria li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #666;
}

.application-criteria li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #0066CC;
    font-weight: bold;
}

.contact-sponsorship h3 {
    color: #0066CC;
    margin-bottom: 1rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #DC2626;
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-method:hover {
    background: #0066CC;
    color: white;
    transform: translateX(5px);
}

.application-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.current-sponsorships {
    padding: 5rem 0;
    background: #f8fafc;
}

.sponsorship-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.sponsor-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.sponsor-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 102, 204, 0.15);
}

.sponsor-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #DC2626, #B91C1C);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.sponsor-item h4 {
    color: #0066CC;
    margin-bottom: 0.5rem;
}

/* Responsive adjustments for sponsorship page */
@media (max-width: 768px) {
    .sponsorship-content h1 {
        font-size: 2rem;
    }

    .application-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-methods {
        align-items: center;
    }
}

/* Privacy Policy Styles */
.privacy-policy {
    padding: 120px 0 80px;
    background: white;
    min-height: 100vh;
}

.privacy-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e2e8f0;
}

.privacy-header h1 {
    color: #0066CC;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.last-updated {
    color: #666;
    font-style: italic;
    font-size: 1rem;
}

.privacy-content {
    max-width: 800px;
    margin: 0 auto;
}

.privacy-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 10px;
    border-left: 4px solid #0066CC;
}

.privacy-section h2 {
    color: #0066CC;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.privacy-section p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: #555;
}

.privacy-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.privacy-section li {
    margin-bottom: 0.5rem;
    color: #555;
    line-height: 1.6;
}

.contact-details {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-top: 1rem;
}

.contact-details p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-details i {
    color: #0066CC;
    width: 20px;
}

/* Responsive adjustments for privacy page */
@media (max-width: 768px) {
    .privacy-header h1 {
        font-size: 2rem;
    }

    .privacy-section {
        padding: 1.5rem;
    }

    .privacy-content {
        padding: 0 1rem;
    }
}
